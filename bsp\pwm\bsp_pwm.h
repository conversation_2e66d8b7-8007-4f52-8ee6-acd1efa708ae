/*
 * 立创开发板软硬件资料与相关扩展板软硬件资料官网全部开源
 * 开发板官网：www.lckfb.com
 * 技术支持常驻论坛，任何技术问题欢迎随时交流学习
 * 立创论坛：https://oshwhub.com/forum
 * 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
 * 不靠卖板赚钱，以培养中国工程师为己任
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-03-07     LCKFB-LP    first version
 */

#ifndef __BSP_PWM_H__
#define __BSP_PWM_H__

#include "stm32f4xx.h"

// PWM频率相关宏定义
#define PWM_FREQUENCY_50HZ 50     // 50Hz频率，适合舵机控制
#define PWM_FREQUENCY_1KHZ 1000   // 1KHz频率，适合一般PWM控制
#define PWM_FREQUENCY_10KHZ 10000 // 10KHz频率，适合电机控制

// PWM通道定义
#define PWM_CHANNEL_2 2
#define PWM_CHANNEL_3 3

// 基础PWM函数
void PWM_Init(uint32_t frequency);
void PWM_SetCompare2(uint16_t Compare);
void PWM_SetCompare3(uint16_t Compare);
void PWM_SetDutyCycle2(float duty_cycle);
void PWM_SetDutyCycle3(float duty_cycle);
uint16_t PWM_GetPeriod(void);

// 270度舵机专用控制函数 (针对双270度舵机硬件)
void PWM_SetServo270_CH2(float angle); // 通道2的270度舵机控制 (0-270度)
void PWM_SetServo270_CH3(float angle); // 通道3的270度舵机控制 (0-270度)

// 270度舵机脉宽控制函数 (直接脉宽设置)
void PWM_SetServo270Pulse_CH2(float pulse_ms); // 通道2脉宽控制 (0.5-2.5ms)
void PWM_SetServo270Pulse_CH3(float pulse_ms); // 通道3脉宽控制 (0.5-2.5ms)

// 兼容性函数 (保持向后兼容)
void PWM_SetServoAngleUP(float angle);    // 兼容函数，内部调用270度控制
void PWM_SetServoAngleDOWN(float angle);    // 兼容函数，内部调用270度控制
void PWM_SetServoPulse2(float pulse_ms); // 兼容函数
void PWM_SetServoPulse3(float pulse_ms); // 兼容函数

#endif



