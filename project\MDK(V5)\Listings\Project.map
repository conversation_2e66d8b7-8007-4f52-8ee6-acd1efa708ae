Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    main.o(i.All_init) refers to board.o(i.board_init) for board_init
    main.o(i.All_init) refers to bsp_uart.o(i.uart1_init) for uart1_init
    main.o(i.All_init) refers to led.o(i.LED_init) for LED_init
    main.o(i.All_init) refers to key.o(i.key_init) for key_init
    main.o(i.All_init) refers to bsp_xray.o(i.Xray_init) for Xray_init
    main.o(i.main) refers to main.o(i.All_init) for All_init
    main.o(i.main) refers to bsp_xray.o(i.Xray_On) for Xray_On
    main.o(i.main) refers to bsp_uart.o(i.Process_Received_Data) for Process_Received_Data
    main.o(i.main) refers to key.o(i.Get_KEY) for Get_KEY
    main.o(i.main) refers to led.o(i.LED_OFF) for LED_OFF
    main.o(i.main) refers to led.o(i.LED_ON) for LED_ON
    main.o(i.main) refers to main.o(.data) for .data
    startup_stm32f40xx.o(RESET) refers to startup_stm32f40xx.o(STACK) for __initial_sp
    startup_stm32f40xx.o(RESET) refers to startup_stm32f40xx.o(.text) for Reset_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f40xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f40xx.o(RESET) refers to bsp_uart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f40xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f40xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    bsp_uart.o(i.Process_Received_Data) refers to printf8.o(i.__0printf$8) for __2printf
    bsp_uart.o(i.Process_Received_Data) refers to bsp_uart.o(.data) for .data
    bsp_uart.o(i.Process_Received_Data) refers to bsp_uart.o(.bss) for .bss
    bsp_uart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    bsp_uart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    bsp_uart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    bsp_uart.o(i.USART1_IRQHandler) refers to bsp_uart.o(.data) for .data
    bsp_uart.o(i.USART1_IRQHandler) refers to bsp_uart.o(.bss) for .bss
    bsp_uart.o(i.fputc) refers to stm32f4xx_usart.o(i.USART_SendData) for USART_SendData
    bsp_uart.o(i.fputc) refers to stm32f4xx_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_gpio.o(i.GPIO_StructInit) for GPIO_StructInit
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_DeInit) for USART_DeInit
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_StructInit) for USART_StructInit
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_ClearFlag) for USART_ClearFlag
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    bsp_uart.o(i.uart1_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    bsp_uart.o(i.uart1_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    led.o(i.LED_OFF) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    led.o(i.LED_ON) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    led.o(i.LED_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    led.o(i.LED_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_pwm.o(i.PWM_GetPeriod) refers to bsp_pwm.o(.data) for .data
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    bsp_pwm.o(i.PWM_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    bsp_pwm.o(i.PWM_Init) refers to bsp_pwm.o(.data) for .data
    bsp_pwm.o(i.PWM_SetCompare2) refers to stm32f4xx_tim.o(i.TIM_SetCompare2) for TIM_SetCompare2
    bsp_pwm.o(i.PWM_SetCompare3) refers to stm32f4xx_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    bsp_pwm.o(i.PWM_SetDutyCycle2) refers to bsp_pwm.o(i.PWM_SetCompare2) for PWM_SetCompare2
    bsp_pwm.o(i.PWM_SetDutyCycle2) refers to bsp_pwm.o(.data) for .data
    bsp_pwm.o(i.PWM_SetDutyCycle3) refers to bsp_pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    bsp_pwm.o(i.PWM_SetDutyCycle3) refers to bsp_pwm.o(.data) for .data
    bsp_pwm.o(i.PWM_SetServo270Pulse_CH2) refers to bsp_pwm.o(i.PWM_SetDutyCycle2) for PWM_SetDutyCycle2
    bsp_pwm.o(i.PWM_SetServo270Pulse_CH3) refers to bsp_pwm.o(i.PWM_SetDutyCycle3) for PWM_SetDutyCycle3
    bsp_pwm.o(i.PWM_SetServo270_CH2) refers to bsp_pwm.o(i.PWM_SetDutyCycle2) for PWM_SetDutyCycle2
    bsp_pwm.o(i.PWM_SetServo270_CH3) refers to bsp_pwm.o(i.PWM_SetDutyCycle3) for PWM_SetDutyCycle3
    bsp_pwm.o(i.PWM_SetServoAngleDOWN) refers to bsp_pwm.o(i.PWM_SetServo270_CH3) for PWM_SetServo270_CH3
    bsp_pwm.o(i.PWM_SetServoAngleUP) refers to bsp_pwm.o(i.PWM_SetServo270_CH2) for PWM_SetServo270_CH2
    bsp_pwm.o(i.PWM_SetServoPulse2) refers to bsp_pwm.o(i.PWM_SetServo270Pulse_CH2) for PWM_SetServo270Pulse_CH2
    bsp_pwm.o(i.PWM_SetServoPulse3) refers to bsp_pwm.o(i.PWM_SetServo270Pulse_CH3) for PWM_SetServo270Pulse_CH3
    key.o(i.Get_KEY) refers to stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.Get_KEY) refers to board.o(i.delay_ms) for delay_ms
    key.o(i.key_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    key.o(i.key_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_xray.o(i.Xray_Off) refers to stm32f4xx_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    bsp_xray.o(i.Xray_On) refers to stm32f4xx_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    bsp_xray.o(i.Xray_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_xray.o(i.Xray_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    board.o(i.board_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    board.o(i.delay_1ms) refers to board.o(i.delay_us) for delay_us
    board.o(i.delay_1us) refers to board.o(i.delay_us) for delay_us
    board.o(i.delay_ms) refers to board.o(i.delay_us) for delay_us
    board.o(i.delay_us) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit) refers to stm32f4xx_fsmc.o(.constdata) for .constdata
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for .data
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to bsp_uart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to bsp_uart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to bsp_uart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to bsp_uart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to bsp_uart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to bsp_uart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to bsp_uart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to bsp_uart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to bsp_uart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to bsp_uart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to bsp_uart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to bsp_uart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to bsp_uart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to bsp_uart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to bsp_uart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to bsp_uart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to bsp_uart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to bsp_uart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to bsp_uart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to bsp_uart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to bsp_uart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to bsp_uart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to bsp_uart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to bsp_uart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to bsp_uart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to bsp_uart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to bsp_uart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to bsp_uart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to bsp_uart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to bsp_uart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to bsp_uart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to bsp_uart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to bsp_uart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to bsp_uart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to bsp_uart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to bsp_uart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to bsp_uart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to bsp_uart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to bsp_uart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to bsp_uart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to bsp_uart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to bsp_uart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to bsp_uart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to bsp_uart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f40xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f40xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (132 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing startup_stm32f40xx.o(HEAP), (512 bytes).
    Removing bsp_uart.o(.rev16_text), (4 bytes).
    Removing bsp_uart.o(.revsh_text), (4 bytes).
    Removing bsp_uart.o(.rrx_text), (6 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing bsp_pwm.o(.rev16_text), (4 bytes).
    Removing bsp_pwm.o(.revsh_text), (4 bytes).
    Removing bsp_pwm.o(.rrx_text), (6 bytes).
    Removing bsp_pwm.o(i.PWM_GetPeriod), (12 bytes).
    Removing bsp_pwm.o(i.PWM_Init), (204 bytes).
    Removing bsp_pwm.o(i.PWM_SetCompare2), (10 bytes).
    Removing bsp_pwm.o(i.PWM_SetCompare3), (10 bytes).
    Removing bsp_pwm.o(i.PWM_SetDutyCycle2), (68 bytes).
    Removing bsp_pwm.o(i.PWM_SetDutyCycle3), (68 bytes).
    Removing bsp_pwm.o(i.PWM_SetServo270Pulse_CH2), (48 bytes).
    Removing bsp_pwm.o(i.PWM_SetServo270Pulse_CH3), (48 bytes).
    Removing bsp_pwm.o(i.PWM_SetServo270_CH2), (72 bytes).
    Removing bsp_pwm.o(i.PWM_SetServo270_CH3), (72 bytes).
    Removing bsp_pwm.o(i.PWM_SetServoAngleDOWN), (4 bytes).
    Removing bsp_pwm.o(i.PWM_SetServoAngleUP), (4 bytes).
    Removing bsp_pwm.o(i.PWM_SetServoPulse2), (4 bytes).
    Removing bsp_pwm.o(i.PWM_SetServoPulse3), (4 bytes).
    Removing bsp_pwm.o(.data), (2 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing bsp_xray.o(.rev16_text), (4 bytes).
    Removing bsp_xray.o(.revsh_text), (4 bytes).
    Removing bsp_xray.o(.rrx_text), (6 bytes).
    Removing bsp_xray.o(i.Xray_Off), (16 bytes).
    Removing board.o(.rev16_text), (4 bytes).
    Removing board.o(.revsh_text), (4 bytes).
    Removing board.o(.rrx_text), (6 bytes).
    Removing board.o(i.delay_1ms), (10 bytes).
    Removing board.o(i.delay_1us), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (28 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Cmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonInit), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMACmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DiscModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (30 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_Init), (76 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_RegularChannelConfig), (116 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (40 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (136 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (24 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (14 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (212 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (82 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (204 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (260 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (28 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (154 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (120 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (182 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (136 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f4xx_cec.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cec.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cec.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (32 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (32 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (22 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (16 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (28 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (32 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (96 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (140 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (220 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (454 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (394 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (416 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1180 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (220 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (198 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (252 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (230 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (28 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (24 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (60 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dfsdm.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dfsdm.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (44 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearITPendingBit), (44 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DeInit), (300 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (26 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCmdStatus), (14 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (14 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetITStatus), (84 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ITConfig), (50 bytes).
    Removing stm32f4xx_dma.o(i.DMA_Init), (84 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (16 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (108 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (32 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (44 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (108 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (32 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (36 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (28 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (180 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (16 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (32 bytes).
    Removing stm32f4xx_dsi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dsi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (20 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (116 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (112 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (56 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (64 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashInterfaceCmd), (32 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashSleepModeCmd), (32 bytes).
    Removing stm32f4xx_fmpi2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearFlag), (42 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ClearITPendingBit), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetECC), (18 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_GetITStatus), (52 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_ITConfig), (86 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDCmd), (64 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDDeInit), (40 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDECCCmd), (64 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDInit), (164 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd), (36 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMDeInit), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit), (232 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_NORSRAMStructInit), (48 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDCmd), (36 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDDeInit), (28 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDInit), (120 bytes).
    Removing stm32f4xx_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (340 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (26 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (14 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (22 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (28 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (28 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (24 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (156 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (328 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (162 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (336 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (50 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (34 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (20 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f4xx_lptim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_lptim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (26 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (24 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (20 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (52 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (28 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (168 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (24 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (150 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (98 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (120 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (88 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (16 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (32 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f4xx_qspi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (48 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (32 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (22 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (32 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (92 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (44 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (18 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (40 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (52 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (44 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (68 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (72 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (136 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (68 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (44 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (188 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (164 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (168 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (52 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (40 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (28 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (24 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (24 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (36 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (10 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (44 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (14 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (22 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (20 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (72 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (24 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (4 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (4 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (34 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (14 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (28 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (18 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (24 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (28 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spdifrx.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (58 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (264 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (26 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (196 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (44 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (20 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (46 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (50 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_Cmd), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (428 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (98 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (48 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseInit), (124 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (28 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (32 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

859 unused section(s) (total 31962 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\..\app\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\..\board\board.c                      0x00000000   Number         0  board.o ABSOLUTE
    ..\..\bsp\Xray\bsp_Xray.c                0x00000000   Number         0  bsp_xray.o ABSOLUTE
    ..\..\bsp\key\key.c                      0x00000000   Number         0  key.o ABSOLUTE
    ..\..\bsp\led\LED.c                      0x00000000   Number         0  led.o ABSOLUTE
    ..\..\bsp\pwm\bsp_pwm.c                  0x00000000   Number         0  bsp_pwm.o ABSOLUTE
    ..\..\bsp\uart\bsp_uart.c                0x00000000   Number         0  bsp_uart.o ABSOLUTE
    ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f40xx.s 0x00000000   Number         0  startup_stm32f40xx.o ABSOLUTE
    ..\..\libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cec.c 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp.c 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_aes.c 0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_des.c 0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_tdes.c 0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dbgmcu.c 0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dcmi.c 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dfsdm.c 0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma2d.c 0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dsi.c 0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_fmpi2c.c 0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash.c 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash_md5.c 0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash_sha1.c 0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_lptim.c 0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_ltdc.c 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_qspi.c 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_sai.c 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_sdio.c 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spdifrx.c 0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\..\libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\..\module\stm32f4xx_it.c              0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\..\\app\\main.c                      0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\board\\board.c                   0x00000000   Number         0  board.o ABSOLUTE
    ..\\..\\bsp\\Xray\\bsp_Xray.c            0x00000000   Number         0  bsp_xray.o ABSOLUTE
    ..\\..\\bsp\\key\\key.c                  0x00000000   Number         0  key.o ABSOLUTE
    ..\\..\\bsp\\led\\LED.c                  0x00000000   Number         0  led.o ABSOLUTE
    ..\\..\\bsp\\pwm\\bsp_pwm.c              0x00000000   Number         0  bsp_pwm.o ABSOLUTE
    ..\\..\\bsp\\uart\\bsp_uart.c            0x00000000   Number         0  bsp_uart.o ABSOLUTE
    ..\\..\\libraries\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cec.c 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp.c 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_aes.c 0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_des.c 0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_tdes.c 0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dbgmcu.c 0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dcmi.c 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dfsdm.c 0x00000000   Number         0  stm32f4xx_dfsdm.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma2d.c 0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dsi.c 0x00000000   Number         0  stm32f4xx_dsi.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_fmpi2c.c 0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_fsmc.c 0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash.c 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash_md5.c 0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash_sha1.c 0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_lptim.c 0x00000000   Number         0  stm32f4xx_lptim.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_ltdc.c 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_qspi.c 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_sai.c 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_sdio.c 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spdifrx.c 0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\..\\libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\..\\module\\stm32f4xx_it.c           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f40xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f40xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000228   Section       36  init.o(.text)
    .text                                    0x0800024c   Section        0  llshl.o(.text)
    .text                                    0x0800026a   Section        0  llushr.o(.text)
    i.All_init                               0x0800028a   Section        0  main.o(i.All_init)
    i.BusFault_Handler                       0x080002a8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080002aa   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.GPIO_Init                              0x080002ac   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08000328   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.GPIO_ReadInputDataBit                  0x08000348   Section        0  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x08000356   Section        0  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x0800035c   Section        0  stm32f4xx_gpio.o(i.GPIO_SetBits)
    i.GPIO_StructInit                        0x08000360   Section        0  stm32f4xx_gpio.o(i.GPIO_StructInit)
    i.Get_KEY                                0x08000374   Section        0  key.o(i.Get_KEY)
    i.HardFault_Handler                      0x080003a8   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.LED_OFF                                0x080003ac   Section        0  led.o(i.LED_OFF)
    i.LED_ON                                 0x080003b8   Section        0  led.o(i.LED_ON)
    i.LED_init                               0x080003c4   Section        0  led.o(i.LED_init)
    i.MemManage_Handler                      0x080003f8   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080003fa   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x080003fc   Section        0  misc.o(i.NVIC_Init)
    i.PendSV_Handler                         0x08000464   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Process_Received_Data                  0x08000468   Section        0  bsp_uart.o(i.Process_Received_Data)
    i.RCC_AHB1PeriphClockCmd                 0x08000560   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphResetCmd                 0x0800057c   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    i.RCC_APB2PeriphClockCmd                 0x08000598   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x080005b4   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x080005d0   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08000670   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08000674   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08000675   Thumb Code   168  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_CLKSourceConfig                0x0800072c   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08000748   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemInit                             0x0800074c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.USART1_IRQHandler                      0x080007a8   Section        0  bsp_uart.o(i.USART1_IRQHandler)
    i.USART_ClearFlag                        0x08000828   Section        0  stm32f4xx_usart.o(i.USART_ClearFlag)
    i.USART_ClearITPendingBit                0x08000830   Section        0  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08000840   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_DeInit                           0x08000858   Section        0  stm32f4xx_usart.o(i.USART_DeInit)
    i.USART_GetFlagStatus                    0x08000958   Section        0  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    i.USART_GetITStatus                      0x08000966   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x080009a6   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x080009dc   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08000a98   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.USART_SendData                         0x08000aa0   Section        0  stm32f4xx_usart.o(i.USART_SendData)
    i.USART_StructInit                       0x08000aa8   Section        0  stm32f4xx_usart.o(i.USART_StructInit)
    i.UsageFault_Handler                     0x08000abe   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.Xray_On                                0x08000ac0   Section        0  bsp_xray.o(i.Xray_On)
    i.Xray_init                              0x08000ad0   Section        0  bsp_xray.o(i.Xray_init)
    i.__0printf$8                            0x08000b04   Section        0  printf8.o(i.__0printf$8)
    i.__scatterload_copy                     0x08000b24   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08000b32   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08000b34   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08000b44   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08000b45   Thumb Code   984  printf8.o(i._printf_core)
    i._printf_post_padding                   0x08000f48   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x08000f49   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08000f6c   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08000f6d   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i.board_init                             0x08000f9c   Section        0  board.o(i.board_init)
    i.delay_ms                               0x08000fc4   Section        0  board.o(i.delay_ms)
    i.delay_us                               0x08000fd0   Section        0  board.o(i.delay_us)
    i.fputc                                  0x08001010   Section        0  bsp_uart.o(i.fputc)
    i.key_init                               0x08001034   Section        0  key.o(i.key_init)
    i.main                                   0x0800105c   Section        0  main.o(i.main)
    i.uart1_init                             0x08001084   Section        0  bsp_uart.o(i.uart1_init)
    .data                                    0x20000000   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section        1  main.o(.data)
    .data                                    0x20000016   Section        8  bsp_uart.o(.data)
    .data                                    0x2000001e   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000001e   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000030   Section        4  stdout.o(.data)
    .bss                                     0x20000034   Section       64  bsp_uart.o(.bss)
    STACK                                    0x20000078   Section     1024  startup_stm32f40xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f40xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    CRYP_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    FSMC_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    UART4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    UART5_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    USART2_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    USART3_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    USART6_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f40xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __scatterload                            0x08000229   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000229   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0800024d   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800024d   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0800026b   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800026b   Thumb Code     0  llushr.o(.text)
    All_init                                 0x0800028b   Thumb Code    30  main.o(i.All_init)
    BusFault_Handler                         0x080002a9   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080002ab   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    GPIO_Init                                0x080002ad   Thumb Code   124  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08000329   Thumb Code    32  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    GPIO_ReadInputDataBit                    0x08000349   Thumb Code    14  stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x08000357   Thumb Code     6  stm32f4xx_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x0800035d   Thumb Code     4  stm32f4xx_gpio.o(i.GPIO_SetBits)
    GPIO_StructInit                          0x08000361   Thumb Code    18  stm32f4xx_gpio.o(i.GPIO_StructInit)
    Get_KEY                                  0x08000375   Thumb Code    48  key.o(i.Get_KEY)
    HardFault_Handler                        0x080003a9   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    LED_OFF                                  0x080003ad   Thumb Code     8  led.o(i.LED_OFF)
    LED_ON                                   0x080003b9   Thumb Code     8  led.o(i.LED_ON)
    LED_init                                 0x080003c5   Thumb Code    46  led.o(i.LED_init)
    MemManage_Handler                        0x080003f9   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080003fb   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x080003fd   Thumb Code    98  misc.o(i.NVIC_Init)
    PendSV_Handler                           0x08000465   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Process_Received_Data                    0x08000469   Thumb Code    94  bsp_uart.o(i.Process_Received_Data)
    RCC_AHB1PeriphClockCmd                   0x08000561   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphResetCmd                   0x0800057d   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd)
    RCC_APB2PeriphClockCmd                   0x08000599   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x080005b5   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x080005d1   Thumb Code   148  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08000671   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x0800072d   Thumb Code    28  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08000749   Thumb Code     2  stm32f4xx_it.o(i.SysTick_Handler)
    SystemInit                               0x0800074d   Thumb Code    74  system_stm32f4xx.o(i.SystemInit)
    USART1_IRQHandler                        0x080007a9   Thumb Code   114  bsp_uart.o(i.USART1_IRQHandler)
    USART_ClearFlag                          0x08000829   Thumb Code     8  stm32f4xx_usart.o(i.USART_ClearFlag)
    USART_ClearITPendingBit                  0x08000831   Thumb Code    16  stm32f4xx_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08000841   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_DeInit                             0x08000859   Thumb Code   224  stm32f4xx_usart.o(i.USART_DeInit)
    USART_GetFlagStatus                      0x08000959   Thumb Code    14  stm32f4xx_usart.o(i.USART_GetFlagStatus)
    USART_GetITStatus                        0x08000967   Thumb Code    64  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x080009a7   Thumb Code    54  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x080009dd   Thumb Code   180  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08000a99   Thumb Code     8  stm32f4xx_usart.o(i.USART_ReceiveData)
    USART_SendData                           0x08000aa1   Thumb Code     8  stm32f4xx_usart.o(i.USART_SendData)
    USART_StructInit                         0x08000aa9   Thumb Code    22  stm32f4xx_usart.o(i.USART_StructInit)
    UsageFault_Handler                       0x08000abf   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    Xray_On                                  0x08000ac1   Thumb Code    10  bsp_xray.o(i.Xray_On)
    Xray_init                                0x08000ad1   Thumb Code    48  bsp_xray.o(i.Xray_init)
    __0printf$8                              0x08000b05   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x08000b05   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08000b05   Thumb Code     0  printf8.o(i.__0printf$8)
    __scatterload_copy                       0x08000b25   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08000b33   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08000b35   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    board_init                               0x08000f9d   Thumb Code    36  board.o(i.board_init)
    delay_ms                                 0x08000fc5   Thumb Code    10  board.o(i.delay_ms)
    delay_us                                 0x08000fd1   Thumb Code    56  board.o(i.delay_us)
    fputc                                    0x08001011   Thumb Code    30  bsp_uart.o(i.fputc)
    key_init                                 0x08001035   Thumb Code    36  key.o(i.key_init)
    main                                     0x0800105d   Thumb Code    36  main.o(i.main)
    uart1_init                               0x08001085   Thumb Code   232  bsp_uart.o(i.uart1_init)
    Region$$Table$$Base                      0x08001174   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08001194   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f4xx.o(.data)
    key_value                                0x20000014   Data           1  main.o(.data)
    state                                    0x20000016   Data           1  bsp_uart.o(.data)
    flag                                     0x20000017   Data           1  bsp_uart.o(.data)
    Rx_index                                 0x20000018   Data           1  bsp_uart.o(.data)
    xx                                       0x2000001a   Data           2  bsp_uart.o(.data)
    yy                                       0x2000001c   Data           2  bsp_uart.o(.data)
    __stdout                                 0x20000030   Data           4  stdout.o(.data)
    Rx_buffer                                0x20000034   Data          64  bsp_uart.o(.bss)
    __initial_sp                             0x20000478   Data           0  startup_stm32f40xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000011c8, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00001194, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          282    RESET               startup_stm32f40xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         5599  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         5863    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         5866    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5868    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         5870    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         5871    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         5878    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         5873    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         5875    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         5864    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO          283    .text               startup_stm32f40xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         5882    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000002   PAD
    0x08000228   0x08000228   0x00000024   Code   RO         5895    .text               mc_w.l(init.o)
    0x0800024c   0x0800024c   0x0000001e   Code   RO         5897    .text               mc_w.l(llshl.o)
    0x0800026a   0x0800026a   0x00000020   Code   RO         5899    .text               mc_w.l(llushr.o)
    0x0800028a   0x0800028a   0x0000001e   Code   RO          148    i.All_init          main.o
    0x080002a8   0x080002a8   0x00000002   Code   RO          208    i.BusFault_Handler  stm32f4xx_it.o
    0x080002aa   0x080002aa   0x00000002   Code   RO          209    i.DebugMon_Handler  stm32f4xx_it.o
    0x080002ac   0x080002ac   0x0000007c   Code   RO         2472    i.GPIO_Init         stm32f4xx_gpio.o
    0x08000328   0x08000328   0x00000020   Code   RO         2473    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08000348   0x08000348   0x0000000e   Code   RO         2476    i.GPIO_ReadInputDataBit  stm32f4xx_gpio.o
    0x08000356   0x08000356   0x00000006   Code   RO         2479    i.GPIO_ResetBits    stm32f4xx_gpio.o
    0x0800035c   0x0800035c   0x00000004   Code   RO         2480    i.GPIO_SetBits      stm32f4xx_gpio.o
    0x08000360   0x08000360   0x00000012   Code   RO         2481    i.GPIO_StructInit   stm32f4xx_gpio.o
    0x08000372   0x08000372   0x00000002   PAD
    0x08000374   0x08000374   0x00000034   Code   RO          483    i.Get_KEY           key.o
    0x080003a8   0x080003a8   0x00000002   Code   RO          210    i.HardFault_Handler  stm32f4xx_it.o
    0x080003aa   0x080003aa   0x00000002   PAD
    0x080003ac   0x080003ac   0x0000000c   Code   RO          343    i.LED_OFF           led.o
    0x080003b8   0x080003b8   0x0000000c   Code   RO          344    i.LED_ON            led.o
    0x080003c4   0x080003c4   0x00000034   Code   RO          345    i.LED_init          led.o
    0x080003f8   0x080003f8   0x00000002   Code   RO          211    i.MemManage_Handler  stm32f4xx_it.o
    0x080003fa   0x080003fa   0x00000002   Code   RO          212    i.NMI_Handler       stm32f4xx_it.o
    0x080003fc   0x080003fc   0x00000068   Code   RO          597    i.NVIC_Init         misc.o
    0x08000464   0x08000464   0x00000002   Code   RO          213    i.PendSV_Handler    stm32f4xx_it.o
    0x08000466   0x08000466   0x00000002   PAD
    0x08000468   0x08000468   0x000000f8   Code   RO          290    i.Process_Received_Data  bsp_uart.o
    0x08000560   0x08000560   0x0000001c   Code   RO         3398    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x0800057c   0x0800057c   0x0000001c   Code   RO         3409    i.RCC_APB1PeriphResetCmd  stm32f4xx_rcc.o
    0x08000598   0x08000598   0x0000001c   Code   RO         3410    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x080005b4   0x080005b4   0x0000001c   Code   RO         3412    i.RCC_APB2PeriphResetCmd  stm32f4xx_rcc.o
    0x080005d0   0x080005d0   0x000000a0   Code   RO         3419    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08000670   0x08000670   0x00000002   Code   RO          214    i.SVC_Handler       stm32f4xx_it.o
    0x08000672   0x08000672   0x00000002   PAD
    0x08000674   0x08000674   0x000000b8   Code   RO            4    i.SetSysClock       system_stm32f4xx.o
    0x0800072c   0x0800072c   0x0000001c   Code   RO          601    i.SysTick_CLKSourceConfig  misc.o
    0x08000748   0x08000748   0x00000002   Code   RO          215    i.SysTick_Handler   stm32f4xx_it.o
    0x0800074a   0x0800074a   0x00000002   PAD
    0x0800074c   0x0800074c   0x0000005c   Code   RO            6    i.SystemInit        system_stm32f4xx.o
    0x080007a8   0x080007a8   0x00000080   Code   RO          291    i.USART1_IRQHandler  bsp_uart.o
    0x08000828   0x08000828   0x00000008   Code   RO         5344    i.USART_ClearFlag   stm32f4xx_usart.o
    0x08000830   0x08000830   0x00000010   Code   RO         5345    i.USART_ClearITPendingBit  stm32f4xx_usart.o
    0x08000840   0x08000840   0x00000018   Code   RO         5348    i.USART_Cmd         stm32f4xx_usart.o
    0x08000858   0x08000858   0x00000100   Code   RO         5350    i.USART_DeInit      stm32f4xx_usart.o
    0x08000958   0x08000958   0x0000000e   Code   RO         5351    i.USART_GetFlagStatus  stm32f4xx_usart.o
    0x08000966   0x08000966   0x00000040   Code   RO         5352    i.USART_GetITStatus  stm32f4xx_usart.o
    0x080009a6   0x080009a6   0x00000036   Code   RO         5354    i.USART_ITConfig    stm32f4xx_usart.o
    0x080009dc   0x080009dc   0x000000bc   Code   RO         5355    i.USART_Init        stm32f4xx_usart.o
    0x08000a98   0x08000a98   0x00000008   Code   RO         5362    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08000aa0   0x08000aa0   0x00000008   Code   RO         5365    i.USART_SendData    stm32f4xx_usart.o
    0x08000aa8   0x08000aa8   0x00000016   Code   RO         5371    i.USART_StructInit  stm32f4xx_usart.o
    0x08000abe   0x08000abe   0x00000002   Code   RO          216    i.UsageFault_Handler  stm32f4xx_it.o
    0x08000ac0   0x08000ac0   0x00000010   Code   RO          514    i.Xray_On           bsp_xray.o
    0x08000ad0   0x08000ad0   0x00000034   Code   RO          515    i.Xray_init         bsp_xray.o
    0x08000b04   0x08000b04   0x00000020   Code   RO         5809    i.__0printf$8       mc_w.l(printf8.o)
    0x08000b24   0x08000b24   0x0000000e   Code   RO         5907    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08000b32   0x08000b32   0x00000002   Code   RO         5908    i.__scatterload_null  mc_w.l(handlers.o)
    0x08000b34   0x08000b34   0x0000000e   Code   RO         5909    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08000b42   0x08000b42   0x00000002   PAD
    0x08000b44   0x08000b44   0x00000404   Code   RO         5816    i._printf_core      mc_w.l(printf8.o)
    0x08000f48   0x08000f48   0x00000024   Code   RO         5817    i._printf_post_padding  mc_w.l(printf8.o)
    0x08000f6c   0x08000f6c   0x0000002e   Code   RO         5818    i._printf_pre_padding  mc_w.l(printf8.o)
    0x08000f9a   0x08000f9a   0x00000002   PAD
    0x08000f9c   0x08000f9c   0x00000028   Code   RO          549    i.board_init        board.o
    0x08000fc4   0x08000fc4   0x0000000a   Code   RO          552    i.delay_ms          board.o
    0x08000fce   0x08000fce   0x00000002   PAD
    0x08000fd0   0x08000fd0   0x00000040   Code   RO          553    i.delay_us          board.o
    0x08001010   0x08001010   0x00000024   Code   RO          292    i.fputc             bsp_uart.o
    0x08001034   0x08001034   0x00000028   Code   RO          484    i.key_init          key.o
    0x0800105c   0x0800105c   0x00000028   Code   RO          149    i.main              main.o
    0x08001084   0x08001084   0x000000f0   Code   RO          293    i.uart1_init        bsp_uart.o
    0x08001174   0x08001174   0x00000020   Data   RO         5905    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08001194, Size: 0x00000478, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08001194   0x00000014   Data   RW            7    .data               system_stm32f4xx.o
    0x20000014   0x080011a8   0x00000001   Data   RW          150    .data               main.o
    0x20000015   0x080011a9   0x00000001   PAD
    0x20000016   0x080011aa   0x00000008   Data   RW          295    .data               bsp_uart.o
    0x2000001e   0x080011b2   0x00000010   Data   RW         3451    .data               stm32f4xx_rcc.o
    0x2000002e   0x080011c2   0x00000002   PAD
    0x20000030   0x080011c4   0x00000004   Data   RW         5879    .data               mc_w.l(stdout.o)
    0x20000034        -       0x00000040   Zero   RW          294    .bss                bsp_uart.o
    0x20000074   0x080011c8   0x00000004   PAD
    0x20000078        -       0x00000400   Zero   RW          280    STACK               startup_stm32f40xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       114         12          0          0          0       1771   board.o
       652        182          0          8         64       5951   bsp_uart.o
        68         10          0          0          0       1118   bsp_xray.o
        92          8          0          0          0       1213   key.o
        76         14          0          0          0       1602   led.o
        70          4          0          1          0       1394   main.o
       132          6          0          0          0       2158   misc.o
        36          8        392          0       1024        976   startup_stm32f40xx.o
       198          0          0          0          0       5157   stm32f4xx_gpio.o
        18          0          0          0          0       4830   stm32f4xx_it.o
       272         36          0         16          0       6520   stm32f4xx_rcc.o
       662         40          0          0          0      10295   stm32f4xx_usart.o
       276         34          0         20          0     274789   system_stm32f4xx.o

    ----------------------------------------------------------------------
      2678        <USER>        <GROUP>         48       1092     317774   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0          3          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
      1142         54          0          0          0        352   printf8.o
         0          0          0          4          0          0   stdout.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1398         <USER>          <GROUP>          4          0        648   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1392         70          0          4          0        648   mc_w.l

    ----------------------------------------------------------------------
      1398         <USER>          <GROUP>          4          0        648   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      4076        424        424         52       1092     313866   Grand Totals
      4076        424        424         52       1092     313866   ELF Image Totals
      4076        424        424         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 4500 (   4.39kB)
    Total RW  Size (RW Data + ZI Data)              1144 (   1.12kB)
    Total ROM Size (Code + RO Data + RW Data)       4552 (   4.45kB)

==============================================================================

