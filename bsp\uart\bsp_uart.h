#ifndef __BSP_UART_H__
#define __BSP_UART_H__

#include "stm32f4xx.h"

/************************* 宏定义 *************************/
#define Rx_buffersize 64

/************************ 变量定义************************/
typedef enum
{
    Re_head,
    Re_data,
    Re_tail
} Uart1_Re_State;

typedef enum
{
    proc_over,
    proc_start
} ReceiveFlag;

extern uint16_t xx, yy;
extern Uart1_Re_State state;
extern ReceiveFlag flag;
extern uint8_t Rx_index;

/************************ 函数声明 ************************/
void uart1_init(uint32_t __Baud);
void USART1_IRQHandler(void);
void Process_Received_Data(void);

#endif
