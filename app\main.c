/************************* 头文件 *************************/
#include <stdio.h>
#include "board.h"
#include "bsp_uart.h"
#include "LED.h"
#include "bsp_pwm.h"
#include "key.h"
#include "bsp_Xray.h"

/************************* 宏定义 *************************/

/************************ 变量定义************************/
uint8_t key_value = 0;
/************************ 函数声明 ************************/
void All_init(void);
/************************ 主函数 ************************/

int main(void)
{
	All_init();	
	Xray_On();

	while(1)
	{
		Process_Received_Data();
		key_value = Get_KEY();
		
		if(key_value == 1)
			LED_ON();
		else LED_OFF();
	}
}

void All_init(void)
{
	board_init();
	uart1_init(115200U);
	LED_init();
	key_init();
	Xray_init();
//	PWM_Init(PWM_FREQUENCY_50HZ); // 初始化PWM，50Hz频率适合舵机控制
}
