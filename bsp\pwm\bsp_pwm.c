/*
 * 立创开发板软硬件资料与相关扩展板软硬件资料官网全部开源
 * 开发板官网：www.lckfb.com
 * 技术支持常驻论坛，任何技术问题欢迎随时交流学习
 * 立创论坛：https://oshwhub.com/forum
 * 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
 * 不靠卖板赚钱，以培养中国工程师为己任
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-03-07     LCKFB-LP    first version
 */

#include "bsp_pwm.h"

// 全局变量存储PWM周期值
static uint16_t PWM_Period = 0;

/**
 * @brief  PWM初始化函数 - 适配STM32F407
 * @param  frequency: PWM频率 (Hz)
 * @note   使用TIM2的CH2(PA1)和CH3(PA2)输出PWM
 *         STM32F407的时钟配置：
 *         - APB1时钟：42MHz (TIM2挂载在APB1上)
 *         - TIM2时钟：84MHz (APB1时钟×2，因为APB1预分频不为1)
 * @retval None
 */
void PWM_Init(uint32_t frequency)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    TIM_OCInitTypeDef TIM_OCInitStructure;

    // 1. 使能时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);  // TIM2时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); // GPIOA时钟

    // 2. 配置GPIO为复用功能
    // PA1 -> TIM2_CH2, PA2 -> TIM2_CH3
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource1, GPIO_AF_TIM2);
    GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_TIM2);

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_1 | GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF; // 复用功能模式
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;   // 推挽输出
    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL; // 无上下拉
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 3. 配置定时器时基
    // STM32F407: TIM2时钟频率为84MHz
    // 计算预分频器和周期值以获得指定频率
    uint32_t timer_clock = 84000000; // 84MHz
    uint32_t prescaler = 1;
    uint32_t period;

    // 寻找合适的预分频器和周期值
    do
    {
        period = timer_clock / (prescaler * frequency);
        if (period <= 65535)
            break; // 16位定时器最大值
        prescaler++;
    } while (prescaler <= 65535);

    PWM_Period = period - 1; // 存储周期值供其他函数使用

    TIM_TimeBaseInitStructure.TIM_Period = period - 1;       // ARR
    TIM_TimeBaseInitStructure.TIM_Prescaler = prescaler - 1; // PSC
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;
    TIM_TimeBaseInit(TIM2, &TIM_TimeBaseInitStructure);

    // 4. 配置PWM输出通道
    TIM_OCStructInit(&TIM_OCInitStructure);
    TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1; // PWM模式1
    TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High; // 高电平有效
    TIM_OCInitStructure.TIM_Pulse = 0;                        // 初始占空比为0

    // 初始化通道2和通道3
    TIM_OC2Init(TIM2, &TIM_OCInitStructure);
    TIM_OC3Init(TIM2, &TIM_OCInitStructure);

    // 5. 使能定时器
    TIM_Cmd(TIM2, ENABLE);
}

/**
 * @brief  设置PWM通道2的比较值
 * @param  Compare: 比较值 (0 ~ PWM_Period)
 * @retval None
 */
void PWM_SetCompare2(uint16_t Compare)
{
    TIM_SetCompare2(TIM2, Compare);
}

/**
 * @brief  设置PWM通道3的比较值
 * @param  Compare: 比较值 (0 ~ PWM_Period)
 * @retval None
 */
void PWM_SetCompare3(uint16_t Compare)
{
    TIM_SetCompare3(TIM2, Compare);
}

/**
 * @brief  设置PWM通道2的占空比
 * @param  duty_cycle: 占空比 (0.0 ~ 1.0)
 * @retval None
 */
void PWM_SetDutyCycle2(float duty_cycle)
{
    if (duty_cycle < 0.0f)
        duty_cycle = 0.0f;
    if (duty_cycle > 1.0f)
        duty_cycle = 1.0f;

    uint16_t compare_value = (uint16_t)(duty_cycle * (PWM_Period + 1));
    PWM_SetCompare2(compare_value);
}

/**
 * @brief  设置PWM通道3的占空比
 * @param  duty_cycle: 占空比 (0.0 ~ 1.0)
 * @retval None
 */
void PWM_SetDutyCycle3(float duty_cycle)
{
    if (duty_cycle < 0.0f)
        duty_cycle = 0.0f;
    if (duty_cycle > 1.0f)
        duty_cycle = 1.0f;

    uint16_t compare_value = (uint16_t)(duty_cycle * (PWM_Period + 1));
    PWM_SetCompare3(compare_value);
}

/**
 * @brief  270度舵机控制 - 通道2
 * @param  angle: 角度值 (0-270度)
 * @note   270度舵机参数：
 *         0度   = 0.5ms脉宽 = 2.5%占空比
 *         135度 = 1.5ms脉宽 = 7.5%占空比 (中位)
 *         270度 = 2.5ms脉宽 = 12.5%占空比
 *         线性映射公式：pulse_ms = 0.5 + (angle / 270) * 2.0
 * @retval None
 */
void PWM_SetServo270_CH2(float angle)
{
    // 限制角度范围到0-270度
    if (angle < 0.0f)
        angle = 0.0f;
    if (angle > 270.0f)
        angle = 270.0f;

    // 270度舵机脉宽映射：0度=0.5ms, 270度=2.5ms
    float pulse_ms = 0.5f + (angle / 270.0f) * 2.0f;

    // 转换为占空比并设置 (20ms周期)
    float duty_cycle = pulse_ms / 20.0f;
    PWM_SetDutyCycle2(duty_cycle);
}

/**
 * @brief  270度舵机控制 - 通道3
 * @param  angle: 角度值 (0-270度)
 * @retval None
 */
void PWM_SetServo270_CH3(float angle)
{
    // 限制角度范围到0-270度
    if (angle < 0.0f)
        angle = 0.0f;
    if (angle > 270.0f)
        angle = 270.0f;

    // 270度舵机脉宽映射：0度=0.5ms, 270度=2.5ms
    float pulse_ms = 0.5f + (angle / 270.0f) * 2.0f;

    // 转换为占空比并设置
    float duty_cycle = pulse_ms / 20.0f;
    PWM_SetDutyCycle3(duty_cycle);
}

/**
 * @brief  270度舵机脉宽控制 - 通道2
 * @param  pulse_ms: 脉宽值 (0.5-2.5ms)
 * @note   直接设置脉宽，适用于精确控制
 * @retval None
 */
void PWM_SetServo270Pulse_CH2(float pulse_ms)
{
    // 限制脉宽范围到0.5-2.5ms
    if (pulse_ms < 0.5f)
        pulse_ms = 0.5f;
    if (pulse_ms > 2.5f)
        pulse_ms = 2.5f;

    // 转换为占空比 (20ms周期)
    float duty_cycle = pulse_ms / 20.0f;
    PWM_SetDutyCycle2(duty_cycle);
}

/**
 * @brief  270度舵机脉宽控制 - 通道3
 * @param  pulse_ms: 脉宽值 (0.5-2.5ms)
 * @retval None
 */
void PWM_SetServo270Pulse_CH3(float pulse_ms)
{
    // 限制脉宽范围到0.5-2.5ms
    if (pulse_ms < 0.5f)
        pulse_ms = 0.5f;
    if (pulse_ms > 2.5f)
        pulse_ms = 2.5f;

    // 转换为占空比
    float duty_cycle = pulse_ms / 20.0f;
    PWM_SetDutyCycle3(duty_cycle);
}

/**
 * @brief  兼容性函数 - 通道2角度控制
 * @param  angle: 角度值 (0-270度)
 * @note   内部调用270度舵机控制函数
 * @retval None
 */
void PWM_SetServoAngleUP(float angle)
{
    PWM_SetServo270_CH2(angle);
}

/**
 * @brief  兼容性函数 - 通道3角度控制
 * @param  angle: 角度值 (0-270度)
 * @retval None
 */
void PWM_SetServoAngleDOWN(float angle)
{
    PWM_SetServo270_CH3(angle);
}

/**
 * @brief  兼容性函数 - 通道2脉宽控制
 * @param  pulse_ms: 脉宽值 (毫秒)
 * @retval None
 */
void PWM_SetServoPulse2(float pulse_ms)
{
    PWM_SetServo270Pulse_CH2(pulse_ms);
}

/**
 * @brief  兼容性函数 - 通道3脉宽控制
 * @param  pulse_ms: 脉宽值 (毫秒)
 * @retval None
 */
void PWM_SetServoPulse3(float pulse_ms)
{
    PWM_SetServo270Pulse_CH3(pulse_ms);
}

/**
 * @brief  获取PWM周期值
 * @retval PWM周期值
 */
uint16_t PWM_GetPeriod(void)
{
    return PWM_Period;
}

